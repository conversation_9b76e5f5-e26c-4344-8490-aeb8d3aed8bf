// @ts-check
import { defineConfig } from 'astro/config';
import partytown from "@astrojs/partytown";
// @ts-ignore
import astroI18next from "astro-i18next";

import netlify from "@astrojs/netlify";

// https://astro.build/config
export default defineConfig({
  site: "https://bookea.link",

  integrations: [
    astroI18next(),
    partytown({
      config: {
        forward: ["dataLayer.push", "gtag"],
      },
    }),
  ],

  i18n: {
    locales: ["en", "es"],
    defaultLocale: "en",
  },

  adapter: netlify()
});