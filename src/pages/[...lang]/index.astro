---
import Welcome from '../../components/Welcome.astro';
import Layout from '../../layouts/Layout.astro';
import { useTranslations, getLocalizedPath, type Locale } from "../../i18n/utils";

export async function getStaticPaths() {
  return [
    { params: { lang: undefined } }, // English (no prefix) - generates /
    { params: { lang: 'es' } }       // Spanish - generates /es/
  ];
}

const { lang } = Astro.params;
const currentLocale: Locale = (lang as Locale) || 'en';
const t = useTranslations(currentLocale);

// Override the URL for proper locale detection since we're using dynamic routing
const mockUrl = new URL(lang ? `/${lang}/` : '/', Astro.site || 'http://localhost:4321');
---

<Layout>
  <a href={getLocalizedPath('/holis', currentLocale)} style="position: fixed; top: 80px; right: 20px; z-index: 1000; padding: 8px 16px; background: rgba(255, 255, 255, 0.9); border: 1px solid #ddd; border-radius: 6px; text-decoration: none; color: #333;">
    {t('index.go_to_holis')}
  </a>
	<Welcome />
</Layout>
